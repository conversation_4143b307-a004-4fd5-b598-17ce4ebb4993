#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試腳本：驗證 Excel 檔案修正是否有效
"""

import os
import tempfile
from openpyxl import Workbook, load_workbook
from excel_check_tool import <PERSON><PERSON><PERSON><PERSON><PERSON>

def create_test_excel():
    """創建測試用的 Excel 檔案"""
    wb = Workbook()
    ws = wb.active
    ws.title = "Statistical_data"
    
    # 設置標題行
    ws["A1"] = "Item#"
    ws["B1"] = "Item Name"
    ws["C1"] = "Average"
    ws["H1"] = "Range"
    ws["K1"] = "CPK"
    
    # 添加測試資料
    test_data = [
        ["Item1", "Test Item 1", 0.05, 2.5, 10.0],  # 應該標記為"規格可以卡更緊"
        ["Item2", "Test Item 2", 0.5, 4.0, 5.0],   # 應該標記為"疑似測試不穩"
        ["Item3", "Test Item 3", 0.2, 1.0, 3.0],   # 不符合任何條件
        ["Item4", "Test Item 4", -0.05, 1.5, 12.0], # 應該標記為"規格可以卡更緊"
    ]
    
    for i, (item_id, item_name, avg, range_val, cpk) in enumerate(test_data, start=2):
        ws[f"A{i}"] = item_id
        ws[f"B{i}"] = item_name
        ws[f"C{i}"] = avg
        ws[f"H{i}"] = range_val
        ws[f"K{i}"] = cpk
    
    return wb

def test_excel_processing():
    """測試 Excel 處理功能"""
    print("開始測試 Excel 處理功能...")
    
    # 創建臨時檔案
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
        temp_path = tmp_file.name
    
    try:
        # 創建測試檔案
        print("1. 創建測試 Excel 檔案...")
        wb = create_test_excel()
        wb.save(temp_path)
        wb.close()
        print(f"   測試檔案已創建: {temp_path}")
        
        # 驗證原始檔案可以正常開啟
        print("2. 驗證原始檔案...")
        try:
            test_wb = load_workbook(temp_path)
            test_wb.close()
            print("   ✓ 原始檔案可以正常開啟")
        except Exception as e:
            print(f"   ✗ 原始檔案無法開啟: {e}")
            return False
        
        # 使用修正後的程式處理檔案
        print("3. 使用修正後的程式處理檔案...")
        checker = ExcelChecker()
        success, message = checker.check_file(temp_path)
        
        if not success:
            print(f"   ✗ 處理失敗: {message}")
            return False
        
        print(f"   ✓ 處理成功: {message}")
        
        # 驗證處理後的檔案可以正常開啟
        print("4. 驗證處理後的檔案...")
        try:
            result_wb = load_workbook(temp_path)
            ws = result_wb["Statistical_data"]
            
            # 檢查結果
            print("   檢查處理結果:")
            print(f"   P1: {ws['P1'].value}")
            print(f"   P2: {ws['P2'].value}")
            print(f"   P3: {ws['P3'].value}")
            print(f"   P4: {ws['P4'].value}")
            print(f"   P5: {ws['P5'].value}")
            
            result_wb.close()
            print("   ✓ 處理後的檔案可以正常開啟")
            
        except Exception as e:
            print(f"   ✗ 處理後的檔案無法開啟: {e}")
            return False
        
        print("\n✓ 所有測試通過！檔案處理正常。")
        return True
        
    finally:
        # 清理臨時檔案
        try:
            os.unlink(temp_path)
            print(f"已清理臨時檔案: {temp_path}")
        except:
            pass

if __name__ == "__main__":
    test_excel_processing()
