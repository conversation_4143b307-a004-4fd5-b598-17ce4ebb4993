# Excel 批次檢查工具

基於原始需求文件實現的Python版本Excel批次檢查工具。

## 安裝

```bash
pip install openpyxl
```

## 使用方法

```bash
python excel_check_tool.py
```

## 功能特色

- 圖形化操作界面
- 批次處理多個Excel檔案
- 可調整的判斷參數
- 即時執行記錄
- 進度顯示

## 判斷條件

### 規格可以卡更緊
- average 在設定範圍內（預設: -0.1 ~ 0.1）
- CPK 大於設定值（預設: > 8）

### 疑似測試不穩
- average 在設定範圍內（預設: -1 ~ 1）
- range 大於設定值（預設: > 3）

## 檔案要求

- Excel檔案必須包含 "Statistical_data" 工作表
- A1 儲存格必須為 "Item#"
- C欄: average（平均值）
- K欄: CPK
- H欄: range
- 結果將輸出至P欄

## 錯誤處理

- 自動跳過格式不符的檔案
- 詳細的錯誤訊息記錄
- 非數值資料自動忽略