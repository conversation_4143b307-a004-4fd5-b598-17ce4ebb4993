#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最終測試腳本：驗證改進後的 Excel 處理程式
"""

import os
import shutil
from openpyxl import load_workbook
from excel_check_tool import ExcelChecker

def test_real_file():
    """測試真實的檔案"""
    print("=== 測試真實檔案 ===")
    
    original_file = r"testdatalog\M8351U31U(CC)_GKAL84.1A_PGM25080062_FT1_R0_ALL_20250822070047.xlsx"
    test_file = "final_test_copy.xlsx"
    
    try:
        # 複製原始檔案
        print("1. 複製原始檔案...")
        shutil.copy2(original_file, test_file)
        print(f"   ✓ 已複製到: {test_file}")
        
        # 檢查原始檔案
        print("2. 檢查原始檔案...")
        try:
            wb = load_workbook(test_file)
            ws = wb["Statistical_data"]
            print(f"   ✓ 原始檔案可以開啟")
            print(f"   ✓ 工作表: {wb.sheetnames}")
            print(f"   ✓ A1: {ws['A1'].value}")
            print(f"   ✓ 資料列數: {ws.max_row}")
            wb.close()
        except Exception as e:
            print(f"   ✗ 原始檔案檢查失敗: {e}")
            return False
        
        # 使用改進後的程式處理
        print("3. 使用改進後的程式處理...")
        checker = ExcelChecker()
        success, message = checker.check_file(test_file)
        
        if not success:
            print(f"   ✗ 處理失敗: {message}")
            return False
        
        print(f"   ✓ {message}")
        
        # 檢查處理後的檔案
        print("4. 檢查處理後的檔案...")
        try:
            wb = load_workbook(test_file)
            ws = wb["Statistical_data"]
            print(f"   ✓ 處理後檔案可以開啟")
            print(f"   ✓ P1: {ws['P1'].value}")
            
            # 檢查一些處理結果
            results_found = 0
            for row in range(2, min(20, ws.max_row + 1)):  # 檢查前18行
                p_value = ws[f"P{row}"].value
                if p_value and p_value.strip():
                    results_found += 1
                    print(f"   ✓ P{row}: {p_value}")
                    if results_found >= 5:  # 只顯示前5個結果
                        break
            
            if results_found == 0:
                print("   ⚠ 沒有找到處理結果，可能所有資料都不符合條件")
            else:
                print(f"   ✓ 找到 {results_found} 個處理結果")
            
            wb.close()
            
        except Exception as e:
            print(f"   ✗ 處理後檔案檢查失敗: {e}")
            return False
        
        # 嘗試用 Excel 格式驗證（模擬 Excel 開啟）
        print("5. 進行 Excel 相容性驗證...")
        try:
            # 重新載入檔案，模擬 Excel 開啟過程
            wb = load_workbook(test_file, data_only=False)
            wb.close()
            
            wb = load_workbook(test_file, data_only=True)
            wb.close()
            
            print("   ✓ Excel 相容性驗證通過")
            
        except Exception as e:
            print(f"   ✗ Excel 相容性驗證失敗: {e}")
            return False
        
        print("\n✅ 所有測試通過！改進後的程式運作正常。")
        return True
        
    except Exception as e:
        print(f"\n❌ 測試過程中發生錯誤: {e}")
        return False
        
    finally:
        # 清理測試檔案
        if os.path.exists(test_file):
            try:
                os.remove(test_file)
                print(f"已清理測試檔案: {test_file}")
            except:
                print(f"警告: 無法清理測試檔案: {test_file}")

def main():
    """主函數"""
    print("Excel 檔案處理改進版本測試")
    print("=" * 50)
    
    # 檢查原始檔案是否存在
    original_file = r"testdatalog\M8351U31U(CC)_GKAL84.1A_PGM25080062_FT1_R0_ALL_20250822070047.xlsx"
    if not os.path.exists(original_file):
        print(f"❌ 找不到測試檔案: {original_file}")
        return False
    
    # 執行測試
    success = test_real_file()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 測試完成：改進後的程式可以安全處理 Excel 檔案")
        print("   - 使用備份和恢復機制")
        print("   - 使用臨時檔案進行安全保存")
        print("   - 包含檔案驗證步驟")
        print("   - 改善了錯誤處理")
    else:
        print("❌ 測試失敗：需要進一步調查問題")
    
    return success

if __name__ == "__main__":
    main()
