
# 📘 Excel 批次檢查工具 使用說明

## 1️⃣ 功能簡介  
本工具用於批次處理資料夾內的多個 `*.xlsx` 檔案，並於指定的工作表 **Statistical_data** 中，依照規則檢查資料並將判斷結果輸出到 **P 欄**。  

---

## 2️⃣ 操作流程  
1. **選擇資料夾**  
   - 點擊按鈕 **「選擇資料夾」**。  
   - 選取包含 `*.xlsx` 檔案的資料夾。  

2. **開始執行**  
   - 點擊 **「開始執行」**，程式會自動讀取資料夾內的所有 `*.xlsx` 檔案，依序檢查。  
   - 每個檔案會執行以下步驟：  
     1. 確認是否存在工作表 **Statistical_data**。  
     2. 檢查 **A1** 是否等於 `"Item#"`：  
        - 是 → 繼續處理，並在 **P1** 寫入固定字樣 **「說明」**。  
        - 否 → 跳出訊息 **「表格格式不相符」**，該檔案不處理。  
     3. 從 **第 2 列** 開始依序檢查 `C, <PERSON>, H` 三欄數值，直到遇到空白列為止。  
     4. 根據判斷條件，在對應列的 **P 欄** 輸出結果。  

---

## 3️⃣ 判斷規則  

| 欄位 | 代表意義 |
|------|---------|
| C    | average（平均值） |
| K    | CPK |
| H    | range |

### 預設判斷條件  
1. **規格可以卡更緊**  
   - 當 `average` 介於 **-0.1 ~ 0.1** 且 `CPK > 8`。  

2. **疑似測試不穩**  
   - 當 `average` 介於 **-1 ~ 1** 且 `range > 3`。  

### 判斷邏輯  
- 以第 2 列為例：  
  - 檢查 `C2, K2, H2`  
  - 符合條件則在 `P2` 寫入結果  
- 接著檢查 `C3, K3, H3` → `P3`，依此類推  
- 直到遇到空白列後停止  

---

## 4️⃣ 參數修改  
- 預設值：  
  - `average` 範圍：-0.1 ~ 0.1  
  - `CPK` 門檻：> 8  
  - `average` 範圍：-1 ~ 1  
  - `range` 門檻：> 3  

- 使用者可於程式介面中輸入新的數值，修改判斷範圍。  

---

## 5️⃣ 輸出範例  

| A (Item#) | C (average) | K (CPK) | H (range) | P (結果) |
|-----------|-------------|---------|-----------|----------|
| Item001   | 0.05        | 9       | 2.5       | 規格可以卡更緊 |
| Item002   | 0.8         | 2       | 3.5       | 疑似測試不穩 |
| Item003   | 1.2         | 5       | 2.0       | （空白，無符合條件） |

---

## 6️⃣ 錯誤處理  
- 若檔案中找不到 **Statistical_data** → 顯示「找不到指定工作表」。  
- 若 `A1` ≠ `"Item#"` → 顯示「表格格式不相符」。  
- 若儲存格內容非數值 → 跳過該列。  
