#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import os
import glob
from openpyxl import load_workbook
import warnings
import threading
import shutil
import tempfile
from typing import Optional, Tuple, List

class ExcelChecker:
    def __init__(self):
        self.avg_min = -0.1
        self.avg_max = 0.1
        self.cpk_threshold = 8
        self.avg_unstable_min = -1
        self.avg_unstable_max = 1
        self.range_threshold = 3
        
    def check_file(self, filepath: str) -> Tuple[bool, str]:
        """檢查單個Excel檔案 - 使用最小修改原則避免格式損壞"""
        backup_path = None
        wb = None

        try:
            # 創建備份檔案
            backup_path = filepath + ".backup"
            shutil.copy2(filepath, backup_path)

            # 忽略 openpyxl 的警告訊息
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                # 使用最保守的載入方式，避免修改任何格式
                wb = load_workbook(filepath, data_only=False, keep_vba=True, keep_links=True)

            # 檢查是否存在Statistical_data工作表
            if "Statistical_data" not in wb.sheetnames:
                return False, "找不到指定工作表 Statistical_data"

            ws = wb["Statistical_data"]

            # 檢查A1是否為"Item#"
            if ws["A1"].value != "Item#":
                return False, "表格格式不相符，A1不等於Item#"

            # 收集需要修改的資料，但先不寫入
            changes = {}

            # 檢查P1是否需要修改
            if ws["P1"].value != "說明":
                changes["P1"] = "說明"

            # 從第2列開始檢查
            row = 2
            processed_count = 0

            while True:
                # 檢查是否為空白列
                if ws[f"A{row}"].value is None and ws[f"C{row}"].value is None:
                    break

                # 取得C, K, H欄的值
                avg_val = ws[f"C{row}"].value
                cpk_val = ws[f"K{row}"].value
                range_val = ws[f"H{row}"].value

                result = ""

                # 檢查數值是否有效
                if self._is_valid_number(avg_val) and self._is_valid_number(cpk_val):
                    if (self.avg_min <= avg_val <= self.avg_max and cpk_val > self.cpk_threshold):
                        result = "規格可以卡更緊"

                if not result and self._is_valid_number(avg_val) and self._is_valid_number(range_val):
                    if (self.avg_unstable_min <= avg_val <= self.avg_unstable_max and range_val > self.range_threshold):
                        result = "疑似測試不穩"

                # 只有在值不同時才記錄需要修改
                current_value = ws[f"P{row}"].value or ""
                new_value = result if result else ""
                if current_value != new_value:
                    changes[f"P{row}"] = new_value

                processed_count += 1
                row += 1

            # 如果沒有需要修改的內容，直接返回
            if not changes:
                wb.close()
                if os.path.exists(backup_path):
                    os.remove(backup_path)
                return True, f"檔案已是最新狀態，檢查了 {processed_count} 筆資料"

            # 只修改需要變更的儲存格
            for cell_ref, value in changes.items():
                ws[cell_ref].value = value

            # 直接保存，不使用臨時檔案
            wb.save(filepath)
            wb.close()
            wb = None

            # 簡單驗證檔案是否可以重新開啟
            try:
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    test_wb = load_workbook(filepath, data_only=True)
                    test_wb.close()
            except Exception as e:
                # 如果驗證失敗，恢復備份
                shutil.copy2(backup_path, filepath)
                if os.path.exists(backup_path):
                    os.remove(backup_path)
                return False, f"檔案驗證失敗，已恢復原檔案: {str(e)}"

            # 清理備份檔案
            if os.path.exists(backup_path):
                os.remove(backup_path)

            return True, f"成功處理 {processed_count} 筆資料，修改了 {len(changes)} 個儲存格"

        except Exception as e:
            # 發生錯誤時恢復備份檔案
            if backup_path and os.path.exists(backup_path):
                try:
                    shutil.copy2(backup_path, filepath)
                    os.remove(backup_path)
                except:
                    pass
            return False, f"處理失敗: {str(e)}"
        finally:
            # 確保工作簿被正確關閉
            if wb is not None:
                try:
                    wb.close()
                except:
                    pass
    
    def _is_valid_number(self, value) -> bool:
        """檢查數值是否有效"""
        try:
            return isinstance(value, (int, float)) and value is not None
        except:
            return False
    
    def update_parameters(self, avg_min: float, avg_max: float, cpk_threshold: float,
                         avg_unstable_min: float, avg_unstable_max: float, range_threshold: float):
        """更新判斷參數"""
        self.avg_min = avg_min
        self.avg_max = avg_max
        self.cpk_threshold = cpk_threshold
        self.avg_unstable_min = avg_unstable_min
        self.avg_unstable_max = avg_unstable_max
        self.range_threshold = range_threshold

class ExcelCheckGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Excel 批次檢查工具")
        self.root.geometry("800x600")
        
        self.checker = ExcelChecker()
        self.selected_folder = tk.StringVar()
        
        self.setup_ui()
        
    def setup_ui(self):
        """設置用戶界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 資料夾選擇區域
        folder_frame = ttk.LabelFrame(main_frame, text="資料夾選擇", padding="5")
        folder_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Entry(folder_frame, textvariable=self.selected_folder, width=60, state="readonly").grid(row=0, column=0, padx=5)
        ttk.Button(folder_frame, text="選擇資料夾", command=self.select_folder).grid(row=0, column=1, padx=5)
        
        # 參數設定區域
        param_frame = ttk.LabelFrame(main_frame, text="參數設定", padding="5")
        param_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # 規格可以卡更緊參數
        ttk.Label(param_frame, text="規格可以卡更緊條件:").grid(row=0, column=0, sticky=tk.W, pady=2)
        
        tight_frame = ttk.Frame(param_frame)
        tight_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=2)
        
        ttk.Label(tight_frame, text="average範圍:").grid(row=0, column=0)
        self.avg_min_var = tk.DoubleVar(value=-0.1)
        ttk.Entry(tight_frame, textvariable=self.avg_min_var, width=10).grid(row=0, column=1, padx=2)
        ttk.Label(tight_frame, text="~").grid(row=0, column=2)
        self.avg_max_var = tk.DoubleVar(value=0.1)
        ttk.Entry(tight_frame, textvariable=self.avg_max_var, width=10).grid(row=0, column=3, padx=2)
        
        ttk.Label(tight_frame, text="CPK >").grid(row=0, column=4, padx=(10,2))
        self.cpk_var = tk.DoubleVar(value=8)
        ttk.Entry(tight_frame, textvariable=self.cpk_var, width=10).grid(row=0, column=5, padx=2)
        
        # 疑似測試不穩參數
        ttk.Label(param_frame, text="疑似測試不穩條件:").grid(row=2, column=0, sticky=tk.W, pady=(10,2))
        
        unstable_frame = ttk.Frame(param_frame)
        unstable_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=2)
        
        ttk.Label(unstable_frame, text="average範圍:").grid(row=0, column=0)
        self.avg_unstable_min_var = tk.DoubleVar(value=-1)
        ttk.Entry(unstable_frame, textvariable=self.avg_unstable_min_var, width=10).grid(row=0, column=1, padx=2)
        ttk.Label(unstable_frame, text="~").grid(row=0, column=2)
        self.avg_unstable_max_var = tk.DoubleVar(value=1)
        ttk.Entry(unstable_frame, textvariable=self.avg_unstable_max_var, width=10).grid(row=0, column=3, padx=2)
        
        ttk.Label(unstable_frame, text="range >").grid(row=0, column=4, padx=(10,2))
        self.range_var = tk.DoubleVar(value=3)
        ttk.Entry(unstable_frame, textvariable=self.range_var, width=10).grid(row=0, column=5, padx=2)
        
        # 執行按鈕
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=10)
        
        self.start_button = ttk.Button(button_frame, text="開始執行", command=self.start_processing)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="清除記錄", command=self.clear_log).pack(side=tk.LEFT, padx=5)
        
        # 進度條
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # 記錄區域
        log_frame = ttk.LabelFrame(main_frame, text="執行記錄", padding="5")
        log_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        
        # 記錄文字區域
        self.log_text = tk.Text(log_frame, height=15, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 設置權重讓界面可以調整大小
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(4, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
    
    def select_folder(self):
        """選擇資料夾"""
        folder = filedialog.askdirectory()
        if folder:
            self.selected_folder.set(folder)
            self.log(f"已選擇資料夾: {folder}")
    
    def log(self, message: str):
        """記錄訊息"""
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.root.update()
    
    def clear_log(self):
        """清除記錄"""
        self.log_text.delete(1.0, tk.END)
    
    def start_processing(self):
        """開始處理檔案"""
        if not self.selected_folder.get():
            messagebox.showerror("錯誤", "請先選擇資料夾")
            return
        
        # 更新檢查器參數
        try:
            self.checker.update_parameters(
                self.avg_min_var.get(),
                self.avg_max_var.get(), 
                self.cpk_var.get(),
                self.avg_unstable_min_var.get(),
                self.avg_unstable_max_var.get(),
                self.range_var.get()
            )
        except tk.TclError:
            messagebox.showerror("錯誤", "請檢查參數設定是否正確")
            return
        
        # 在新線程中執行處理，避免界面凍結
        self.start_button.config(state="disabled")
        self.progress.start()
        
        thread = threading.Thread(target=self.process_files)
        thread.daemon = True
        thread.start()
    
    def process_files(self):
        """處理所有Excel檔案"""
        folder = self.selected_folder.get()
        excel_files = glob.glob(os.path.join(folder, "*.xlsx"))
        
        if not excel_files:
            self.log("資料夾中沒有找到.xlsx檔案")
            self.finish_processing()
            return
        
        self.log(f"找到 {len(excel_files)} 個Excel檔案，開始處理...")
        
        success_count = 0
        error_count = 0
        
        for i, filepath in enumerate(excel_files, 1):
            filename = os.path.basename(filepath)
            self.log(f"[{i}/{len(excel_files)}] 正在處理: {filename}")
            
            success, message = self.checker.check_file(filepath)
            if success:
                self.log(f"✓ {filename}: {message}")
                success_count += 1
            else:
                self.log(f"✗ {filename}: {message}")
                error_count += 1
        
        self.log(f"\n=== 處理完成 ===")
        self.log(f"成功: {success_count} 個檔案")
        self.log(f"失敗: {error_count} 個檔案")
        
        self.finish_processing()
    
    def finish_processing(self):
        """完成處理"""
        self.progress.stop()
        self.start_button.config(state="normal")

def main():
    root = tk.Tk()
    app = ExcelCheckGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()