


1. 初步規則 如果averqge介於-0.1~0.1 且CPK大於8 代表可以卡更緊

2. averqge介於-1~1 range 大於3 顯示 疑似測試不穩



1. 執行程式後有兩個按鈕 1.選擇資料夾    2.開始執行
    
   選擇資料夾: 功能可以點選*.xlsx 
   開始執行: 程式會將資料夾內的*.xlsx 打開 並找到sheet名稱為Statistical_data
   後再Statistical_data內執行以下搜尋:   
       
   儲存格C2是平均值,K2是CPK,H2是range 在P1寫入 說明確認A1的內容是Item# 才會執行程式,否則顯示表格格式不相符
   按下開始執行後 會顯示說明,內容如下:
   1.averqge介於-0.1~0.1 且CPK大於8 代表可以卡更緊
   2.averqge介於-1~1 range 大於3 顯示 疑似測試不穩       
   預設值如上數值,但是可以手動修改. 

   判斷條件如下:
   判斷C2,K2,H2 
   如果 C2介於-0.1~0.1 且K2大於8 在儲存格P2寫入 規格可以卡更緊
   如果 C2介於-1~1,且range大於3  在儲存格P2寫入  疑似測試不穩
   往下執行 
   判斷C3,K3,H3 
   如果 C3介於-0.1~0.1 且K2大於8 在儲存格P3寫入 規格可以卡更緊
   如果 C3介於-1~1,且range大於3  在儲存格P3寫入  疑似測試不穩
   往下執行 
   判斷C4,K4,H4 
   如果 C4介於-0.1~0.1 且K4大於8 在儲存格P4寫入 規格可以卡更緊
   如果 C4介於-1~1,且range大於3  在儲存格P4寫入  疑似測試不穩
   依此類推直到出現空格後停止


20250903:
可執行但是速度超級慢 excel_check_tool.py
mac可執行win沒試過  excel_check_tool_v2.py





    

