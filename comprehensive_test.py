#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
綜合測試腳本：測試修正後的 Excel 處理程式
"""

import os
import tempfile
import shutil
from openpyxl import Workbook, load_workbook
from excel_check_tool import <PERSON><PERSON><PERSON><PERSON><PERSON>

def create_comprehensive_test_excel():
    """創建更全面的測試 Excel 檔案"""
    wb = Workbook()
    ws = wb.active
    ws.title = "Statistical_data"
    
    # 設置標題行
    headers = ["Item#", "Item Name", "Average", "D", "E", "F", "G", "Range", "I", "J", "CPK", "L", "M", "N", "O"]
    for i, header in enumerate(headers, start=1):
        ws.cell(row=1, column=i, value=header)
    
    # 添加更多測試資料，包括邊界情況
    test_data = [
        ["Item1", "正常項目1", 0.05, 1, 2, 3, 4, 2.5, 6, 7, 10.0, 9, 10, 11, 12],  # 規格可以卡更緊
        ["Item2", "不穩項目1", 0.5, 1, 2, 3, 4, 4.0, 6, 7, 5.0, 9, 10, 11, 12],   # 疑似測試不穩
        ["Item3", "正常項目2", 0.2, 1, 2, 3, 4, 1.0, 6, 7, 3.0, 9, 10, 11, 12],   # 不符合條件
        ["Item4", "邊界項目1", -0.1, 1, 2, 3, 4, 1.5, 6, 7, 8.1, 9, 10, 11, 12], # 邊界：規格可以卡更緊
        ["Item5", "邊界項目2", 0.1, 1, 2, 3, 4, 3.1, 6, 7, 7.9, 9, 10, 11, 12],  # 邊界：疑似測試不穩
        ["Item6", "空值項目", None, 1, 2, 3, 4, None, 6, 7, None, 9, 10, 11, 12], # 包含空值
        ["Item7", "字串項目", "text", 1, 2, 3, 4, "text", 6, 7, "text", 9, 10, 11, 12], # 包含非數值
    ]
    
    for i, row_data in enumerate(test_data, start=2):
        for j, value in enumerate(row_data, start=1):
            ws.cell(row=i, column=j, value=value)
    
    return wb

def test_file_integrity(filepath):
    """測試檔案完整性"""
    try:
        # 嘗試用 openpyxl 開啟
        wb = load_workbook(filepath)
        ws = wb["Statistical_data"]
        
        # 檢查基本結構
        if ws["A1"].value != "Item#":
            wb.close()
            return False, "A1 值不正確"
        
        # 檢查是否有資料
        if ws["A2"].value is None:
            wb.close()
            return False, "沒有資料"
        
        wb.close()
        return True, "檔案完整性檢查通過"
        
    except Exception as e:
        return False, f"檔案完整性檢查失敗: {str(e)}"

def main():
    """主測試函數"""
    print("=== Excel 檔案修正綜合測試 ===\n")
    
    # 創建測試目錄
    test_dir = tempfile.mkdtemp(prefix="excel_test_")
    print(f"測試目錄: {test_dir}")
    
    try:
        # 創建測試檔案
        test_file = os.path.join(test_dir, "test_data.xlsx")
        print("\n1. 創建測試檔案...")
        
        wb = create_comprehensive_test_excel()
        wb.save(test_file)
        wb.close()
        print(f"   ✓ 測試檔案已創建: {test_file}")
        
        # 檢查原始檔案
        print("\n2. 檢查原始檔案完整性...")
        success, message = test_file_integrity(test_file)
        if not success:
            print(f"   ✗ {message}")
            return False
        print(f"   ✓ {message}")
        
        # 使用修正後的程式處理
        print("\n3. 使用修正後的程式處理...")
        checker = ExcelChecker()
        success, message = checker.check_file(test_file)
        
        if not success:
            print(f"   ✗ 處理失敗: {message}")
            return False
        
        print(f"   ✓ {message}")
        
        # 檢查處理後的檔案
        print("\n4. 檢查處理後的檔案完整性...")
        success, message = test_file_integrity(test_file)
        if not success:
            print(f"   ✗ {message}")
            return False
        print(f"   ✓ {message}")
        
        # 檢查處理結果
        print("\n5. 檢查處理結果...")
        wb = load_workbook(test_file)
        ws = wb["Statistical_data"]
        
        expected_results = {
            2: "規格可以卡更緊",  # Item1
            3: "疑似測試不穩",    # Item2
            4: "",              # Item3 (不符合條件)
            5: "規格可以卡更緊",  # Item4
            6: "",              # Item5 (邊界情況)
            7: "",              # Item6 (空值)
            8: "",              # Item7 (字串)
        }
        
        all_correct = True
        for row, expected in expected_results.items():
            actual = ws[f"P{row}"].value or ""
            if actual == expected:
                print(f"   ✓ P{row}: '{actual}' (正確)")
            else:
                print(f"   ✗ P{row}: 期望 '{expected}', 實際 '{actual}'")
                all_correct = False
        
        wb.close()
        
        if all_correct:
            print("\n✓ 所有測試通過！修正後的程式運作正常。")
            return True
        else:
            print("\n✗ 部分測試失敗。")
            return False
            
    except Exception as e:
        print(f"\n✗ 測試過程中發生錯誤: {e}")
        return False
        
    finally:
        # 清理測試目錄
        try:
            shutil.rmtree(test_dir)
            print(f"\n已清理測試目錄: {test_dir}")
        except:
            print(f"\n警告: 無法清理測試目錄: {test_dir}")

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 測試完成：修正後的程式應該可以正常運作，不會產生損壞的 Excel 檔案。")
    else:
        print("\n❌ 測試失敗：需要進一步調查問題。")
