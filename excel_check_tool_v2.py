#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import os
import glob
from openpyxl import load_workbook
import threading
from typing import Optional, Tuple, List, Dict, Any
import gc
import time
from queue import Queue
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing as mp

# 全域函數用於多進程處理
def process_single_file(args):
    """單個檔案處理函數，用於多進程"""
    filepath, params = args
    checker = ExcelChecker()
    checker.update_parameters(*params)
    return filepath, checker.check_file_optimized(filepath)

class ExcelChecker:
    def __init__(self):
        self.avg_min = -0.1
        self.avg_max = 0.1
        self.cpk_threshold = 8
        self.avg_unstable_min = -1
        self.avg_unstable_max = 1
        self.range_threshold = 3
        
    def check_file_optimized(self, filepath: str, progress_callback=None) -> <PERSON><PERSON>[bool, str]:
        """最優化版檔案檢查 - 大幅提升性能"""
        wb = None
        try:
            start_time = time.time()
            
            # 只讀模式載入，減少記憶體使用
            wb = load_workbook(filepath, data_only=True, read_only=False)
            
            # 檢查是否存在Statistical_data工作表
            if "Statistical_data" not in wb.sheetnames:
                wb.close()
                return False, "找不到指定工作表 Statistical_data"
            
            ws = wb["Statistical_data"]
            
            # 檢查A1是否為"Item#"
            if ws["A1"].value != "Item#":
                wb.close()
                return False, "表格格式不相符，A1不等於Item#"
            
            # 在P1寫入固定字樣
            ws["P1"] = "說明"
            
            # 超高效批量資料處理
            processed_count = self._process_data_ultra_fast(ws, progress_callback)
            
            # 儲存檔案
            wb.save(filepath)
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            return True, f"成功處理 {processed_count} 筆資料 (耗時: {processing_time:.2f}秒)"
            
        except Exception as e:
            return False, f"處理失敗: {str(e)}"
        finally:
            # 確保工作簿被正確關閉
            if wb:
                try:
                    wb.close()
                except:
                    pass
            # 強制垃圾回收
            gc.collect()
    
    def _process_data_ultra_fast(self, ws, progress_callback=None) -> int:
        """超高效批量處理資料 - 使用原生openpyxl範圍讀取"""
        # 快速找出實際資料範圍
        max_row = self._find_data_end_fast(ws)
        
        if max_row < 2:
            return 0
        
        try:
            # 一次性讀取所有需要的範圍 - 這是最快的方法
            a_range = ws[f'A2:A{max_row}']
            c_range = ws[f'C2:C{max_row}']
            k_range = ws[f'K2:K{max_row}']
            h_range = ws[f'H2:H{max_row}']
            
            # 將範圍轉換為值列表 - 比逐個存取快很多
            a_values = [cell[0].value for cell in a_range]
            c_values = [cell[0].value for cell in c_range]
            k_values = [cell[0].value for cell in k_range]
            h_values = [cell[0].value for cell in h_range]
            
            # 批量處理所有數據
            results = []
            processed_count = 0
            
            # 使用列表推導式和向量化操作來提升速度
            for i, (a_val, avg_val, cpk_val, range_val) in enumerate(zip(a_values, c_values, k_values, h_values)):
                # 檢查是否為空白列
                if a_val is None and avg_val is None:
                    results.append("")
                    processed_count += 1
                    continue
                
                # 快速規則檢查
                result = self._evaluate_row_fast(avg_val, cpk_val, range_val)
                results.append(result)
                processed_count += 1
                
                # 每1000行回報一次進度
                if progress_callback and i % 1000 == 0:
                    progress = i / len(a_values)
                    progress_callback(progress)
            
            # 超高效批量寫入 - 一次性設定整個範圍
            if results:
                p_range = ws[f'P2:P{max_row}']
                for i, result in enumerate(results):
                    if i < len(p_range):
                        p_range[i][0].value = result
            
            # 最終進度更新
            if progress_callback:
                progress_callback(1.0)
                
            return processed_count
            
        except Exception as e:
            # 如果批量處理失敗，回退到原始方法
            return self._process_data_fallback(ws, progress_callback)
    
    def _process_data_fallback(self, ws, progress_callback=None) -> int:
        """備用處理方法"""
        max_row = ws.max_row
        if max_row < 2:
            return 0
        
        processed_count = 0
        
        for row in range(2, max_row + 1):
            a_val = ws[f"A{row}"].value
            avg_val = ws[f"C{row}"].value
            cpk_val = ws[f"K{row}"].value
            range_val = ws[f"H{row}"].value
            
            if a_val is None and avg_val is None:
                ws[f"P{row}"] = ""
                processed_count += 1
                continue
            
            result = self._evaluate_row_fast(avg_val, cpk_val, range_val)
            ws[f"P{row}"] = result
            processed_count += 1
            
            if progress_callback and row % 100 == 0:
                progress = (row - 1) / (max_row - 1)
                progress_callback(progress)
        
        return processed_count
    
    def _find_data_end_fast(self, ws) -> int:
        """超快速找出資料結束位置"""
        # 使用工作表的max_row屬性
        max_row = ws.max_row
        
        # 如果max_row很大，使用二分搜尋法找到真正的結束位置
        if max_row > 1000:
            # 從後往前檢查，找到第一個非空行
            for row in range(max_row, max(1, max_row - 1000), -1):
                try:
                    if ws[f"A{row}"].value is not None or ws[f"C{row}"].value is not None:
                        return row
                except:
                    continue
        
        return max_row
    
    def _evaluate_row_fast(self, avg_val, cpk_val, range_val) -> str:
        """高效評估單行資料 - 減少函數呼叫"""
        # 內聯數值檢查以提升速度
        if isinstance(avg_val, (int, float)) and avg_val is not None:
            if isinstance(cpk_val, (int, float)) and cpk_val is not None:
                if self.avg_min <= avg_val <= self.avg_max and cpk_val > self.cpk_threshold:
                    return "規格可以卡更緊"
            
            if isinstance(range_val, (int, float)) and range_val is not None:
                if self.avg_unstable_min <= avg_val <= self.avg_unstable_max and range_val > self.range_threshold:
                    return "疑似測試不穩"
        
        return ""
    
    def update_parameters(self, avg_min: float, avg_max: float, cpk_threshold: float,
                         avg_unstable_min: float, avg_unstable_max: float, range_threshold: float):
        """更新判斷參數"""
        self.avg_min = avg_min
        self.avg_max = avg_max
        self.cpk_threshold = cpk_threshold
        self.avg_unstable_min = avg_unstable_min
        self.avg_unstable_max = avg_unstable_max
        self.range_threshold = range_threshold

class ExcelCheckGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Excel 批次檢查工具 (超級優化版 v2.0)")
        self.root.geometry("900x700")
        
        self.checker = ExcelChecker()
        self.selected_folder = tk.StringVar()
        self.log_queue = Queue()
        self.processing_thread = None
        
        self.setup_ui()
        self.setup_log_updater()
        
    def setup_ui(self):
        """設置用戶界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 標題
        title_label = ttk.Label(main_frame, text="Excel 批次檢查工具 (超級優化版)", font=("", 14, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))
        
        # 資料夾選擇區域
        folder_frame = ttk.LabelFrame(main_frame, text="資料夾選擇", padding="5")
        folder_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Entry(folder_frame, textvariable=self.selected_folder, width=70, state="readonly").grid(row=0, column=0, padx=5)
        ttk.Button(folder_frame, text="選擇資料夾", command=self.select_folder).grid(row=0, column=1, padx=5)
        
        # 參數設定區域
        param_frame = ttk.LabelFrame(main_frame, text="參數設定", padding="5")
        param_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # 規格可以卡更緊參數
        ttk.Label(param_frame, text="規格可以卡更緊條件:", font=("", 9, "bold")).grid(row=0, column=0, sticky=tk.W, pady=2)
        
        tight_frame = ttk.Frame(param_frame)
        tight_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=2)
        
        ttk.Label(tight_frame, text="average範圍:").grid(row=0, column=0)
        self.avg_min_var = tk.DoubleVar(value=-0.1)
        ttk.Entry(tight_frame, textvariable=self.avg_min_var, width=10).grid(row=0, column=1, padx=2)
        ttk.Label(tight_frame, text="~").grid(row=0, column=2)
        self.avg_max_var = tk.DoubleVar(value=0.1)
        ttk.Entry(tight_frame, textvariable=self.avg_max_var, width=10).grid(row=0, column=3, padx=2)
        
        ttk.Label(tight_frame, text="CPK >").grid(row=0, column=4, padx=(10,2))
        self.cpk_var = tk.DoubleVar(value=8)
        ttk.Entry(tight_frame, textvariable=self.cpk_var, width=10).grid(row=0, column=5, padx=2)
        
        # 疑似測試不穩參數
        ttk.Label(param_frame, text="疑似測試不穩條件:", font=("", 9, "bold")).grid(row=2, column=0, sticky=tk.W, pady=(10,2))
        
        unstable_frame = ttk.Frame(param_frame)
        unstable_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=2)
        
        ttk.Label(unstable_frame, text="average範圍:").grid(row=0, column=0)
        self.avg_unstable_min_var = tk.DoubleVar(value=-1)
        ttk.Entry(unstable_frame, textvariable=self.avg_unstable_min_var, width=10).grid(row=0, column=1, padx=2)
        ttk.Label(unstable_frame, text="~").grid(row=0, column=2)
        self.avg_unstable_max_var = tk.DoubleVar(value=1)
        ttk.Entry(unstable_frame, textvariable=self.avg_unstable_max_var, width=10).grid(row=0, column=3, padx=2)
        
        ttk.Label(unstable_frame, text="range >").grid(row=0, column=4, padx=(10,2))
        self.range_var = tk.DoubleVar(value=3)
        ttk.Entry(unstable_frame, textvariable=self.range_var, width=10).grid(row=0, column=5, padx=2)
        
        # 處理選項 - 加強版
        options_frame = ttk.LabelFrame(main_frame, text="處理選項 (性能設定)", padding="5")
        options_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # 多進程選項
        mp_frame = ttk.Frame(options_frame)
        mp_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=2)
        
        self.use_multiprocessing = tk.BooleanVar(value=True)
        ttk.Checkbutton(mp_frame, text="使用多進程處理", variable=self.use_multiprocessing).grid(row=0, column=0, sticky=tk.W)
        
        ttk.Label(mp_frame, text="進程數:").grid(row=0, column=1, padx=(20,5))
        self.cpu_count_var = tk.IntVar(value=min(4, mp.cpu_count()))
        cpu_spinbox = ttk.Spinbox(mp_frame, from_=1, to=mp.cpu_count(), textvariable=self.cpu_count_var, width=5)
        cpu_spinbox.grid(row=0, column=2, padx=5)
        
        # 性能資訊
        perf_label = ttk.Label(options_frame, text=f"系統CPU核心數: {mp.cpu_count()}", font=("", 8))
        perf_label.grid(row=1, column=0, sticky=tk.W, pady=(5,0))
        
        # 執行按鈕
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=15)
        
        self.start_button = ttk.Button(button_frame, text="🚀 開始執行", command=self.start_processing, style="Accent.TButton")
        self.start_button.pack(side=tk.LEFT, padx=10)
        
        ttk.Button(button_frame, text="🗑️ 清除記錄", command=self.clear_log).pack(side=tk.LEFT, padx=5)
        
        # 進度顯示區域 - 加強版
        progress_frame = ttk.LabelFrame(main_frame, text="處理進度", padding="5")
        progress_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # 總體進度
        ttk.Label(progress_frame, text="總體進度:").grid(row=0, column=0, sticky=tk.W)
        self.overall_progress = ttk.Progressbar(progress_frame, mode='determinate', length=400)
        self.overall_progress.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10,5))
        self.overall_progress_label = ttk.Label(progress_frame, text="0/0")
        self.overall_progress_label.grid(row=0, column=2)
        
        # 當前檔案進度
        ttk.Label(progress_frame, text="當前檔案:").grid(row=1, column=0, sticky=tk.W)
        self.file_progress = ttk.Progressbar(progress_frame, mode='determinate', length=400)
        self.file_progress.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10,5), pady=(5,0))
        self.file_progress_label = ttk.Label(progress_frame, text="0%")
        self.file_progress_label.grid(row=1, column=2, pady=(5,0))
        
        progress_frame.columnconfigure(1, weight=1)
        
        # 記錄區域
        log_frame = ttk.LabelFrame(main_frame, text="執行記錄", padding="5")
        log_frame.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        
        # 記錄文字區域 - 加強版
        self.log_text = tk.Text(log_frame, height=16, wrap=tk.WORD, font=("Consolas", 9))
        scrollbar_y = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        scrollbar_x = ttk.Scrollbar(log_frame, orient=tk.HORIZONTAL, command=self.log_text.xview)
        self.log_text.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar_y.grid(row=0, column=1, sticky=(tk.N, tk.S))
        scrollbar_x.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # 設置權重讓界面可以調整大小
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(6, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
    
    def setup_log_updater(self):
        """設置記錄更新器 - 高效版本"""
        def update_log():
            messages_updated = 0
            try:
                # 一次處理多個訊息，提升UI響應性
                while messages_updated < 10:  # 限制每次更新的訊息數量
                    message = self.log_queue.get_nowait()
                    self.log_text.insert(tk.END, message + "\n")
                    messages_updated += 1
                
                if messages_updated > 0:
                    self.log_text.see(tk.END)
                    
            except:
                pass
            finally:
                # 更頻繁的更新以確保即時性
                self.root.after(50, update_log)
        
        self.root.after(50, update_log)
    
    def select_folder(self):
        """選擇資料夾"""
        folder = filedialog.askdirectory()
        if folder:
            self.selected_folder.set(folder)
            # 快速掃描檔案數量
            excel_files = glob.glob(os.path.join(folder, "*.xlsx"))
            self.queue_log(f"已選擇資料夾: {folder}")
            self.queue_log(f"發現 {len(excel_files)} 個Excel檔案")
    
    def queue_log(self, message: str):
        """將訊息加入佇列"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_queue.put(f"[{timestamp}] {message}")
    
    def clear_log(self):
        """清除記錄"""
        self.log_text.delete(1.0, tk.END)
        # 清空佇列
        while not self.log_queue.empty():
            try:
                self.log_queue.get_nowait()
            except:
                break
    
    def start_processing(self):
        """開始處理檔案"""
        if not self.selected_folder.get():
            messagebox.showerror("錯誤", "請先選擇資料夾")
            return
        
        # 更新檢查器參數
        try:
            self.checker.update_parameters(
                self.avg_min_var.get(),
                self.avg_max_var.get(), 
                self.cpk_var.get(),
                self.avg_unstable_min_var.get(),
                self.avg_unstable_max_var.get(),
                self.range_var.get()
            )
        except tk.TclError:
            messagebox.showerror("錯誤", "請檢查參數設定是否正確")
            return
        
        # 重置進度
        self.overall_progress['value'] = 0
        self.file_progress['value'] = 0
        self.overall_progress_label.config(text="0/0")
        self.file_progress_label.config(text="0%")
        
        # 在新線程中執行處理
        self.start_button.config(state="disabled", text="處理中...")
        
        if self.use_multiprocessing.get():
            self.processing_thread = threading.Thread(target=self.process_files_multiprocess)
        else:
            self.processing_thread = threading.Thread(target=self.process_files_optimized)
        self.processing_thread.daemon = True
        self.processing_thread.start()
    
    def process_files_optimized(self):
        """單線程優化處理"""
        folder = self.selected_folder.get()
        excel_files = glob.glob(os.path.join(folder, "*.xlsx"))
        
        if not excel_files:
            self.queue_log("❌ 資料夾中沒有找到.xlsx檔案")
            self.finish_processing()
            return
        
        self.queue_log(f"🔍 找到 {len(excel_files)} 個Excel檔案，開始單線程處理...")
        self.overall_progress['maximum'] = len(excel_files)
        
        success_count = 0
        error_count = 0
        total_start_time = time.time()
        
        for i, filepath in enumerate(excel_files, 1):
            filename = os.path.basename(filepath)
            self.queue_log(f"📄 [{i}/{len(excel_files)}] 處理中: {filename}")
            
            # 重置檔案進度
            self.file_progress['value'] = 0
            self.file_progress['maximum'] = 100
            
            def progress_callback(progress):
                self.file_progress['value'] = progress * 100
                self.file_progress_label.config(text=f"{progress*100:.0f}%")
            
            success, message = self.checker.check_file_optimized(filepath, progress_callback)
            
            if success:
                self.queue_log(f"✅ {filename}: {message}")
                success_count += 1
            else:
                self.queue_log(f"❌ {filename}: {message}")
                error_count += 1
            
            # 更新總體進度
            self.overall_progress['value'] = i
            self.overall_progress_label.config(text=f"{i}/{len(excel_files)}")
            self.file_progress['value'] = 100
            self.file_progress_label.config(text="100%")
        
        total_time = time.time() - total_start_time
        
        self.queue_log(f"\n🎉 === 單線程處理完成 ===")
        self.queue_log(f"✅ 成功: {success_count} 個檔案")
        self.queue_log(f"❌ 失敗: {error_count} 個檔案")
        self.queue_log(f"⏱️ 總處理時間: {total_time:.2f} 秒")
        self.queue_log(f"📊 平均每檔案: {total_time/len(excel_files):.2f} 秒")
        
        self.finish_processing()
    
    def process_files_multiprocess(self):
        """多進程處理所有Excel檔案 - 超高效版本"""
        folder = self.selected_folder.get()
        excel_files = glob.glob(os.path.join(folder, "*.xlsx"))
        
        if not excel_files:
            self.queue_log("❌ 資料夾中沒有找到.xlsx檔案")
            self.finish_processing()
            return
        
        cpu_count = self.cpu_count_var.get()
        self.queue_log(f"🚀 找到 {len(excel_files)} 個Excel檔案，使用 {cpu_count} 個進程開始高速處理...")
        self.overall_progress['maximum'] = len(excel_files)
        
        # 準備參數
        params = (
            self.avg_min_var.get(),
            self.avg_max_var.get(),
            self.cpk_var.get(),
            self.avg_unstable_min_var.get(),
            self.avg_unstable_max_var.get(),
            self.range_var.get()
        )
        
        args = [(filepath, params) for filepath in excel_files]
        
        success_count = 0
        error_count = 0
        total_start_time = time.time()
        
        try:
            with ProcessPoolExecutor(max_workers=cpu_count) as executor:
                # 提交所有任務
                future_to_filepath = {executor.submit(process_single_file, arg): arg[0] for arg in args}
                
                # 處理完成的任務
                completed = 0
                for future in as_completed(future_to_filepath):
                    completed += 1
                    filepath = future_to_filepath[future]
                    filename = os.path.basename(filepath)
                    
                    try:
                        result_filepath, (success, message) = future.result()
                        
                        if success:
                            self.queue_log(f"✅ [{completed}/{len(excel_files)}] {filename}: {message}")
                            success_count += 1
                        else:
                            self.queue_log(f"❌ [{completed}/{len(excel_files)}] {filename}: {message}")
                            error_count += 1
                    except Exception as e:
                        self.queue_log(f"💥 [{completed}/{len(excel_files)}] {filename}: 處理時發生錯誤: {str(e)}")
                        error_count += 1
                    
                    # 更新進度
                    self.overall_progress['value'] = completed
                    self.overall_progress_label.config(text=f"{completed}/{len(excel_files)}")
                    self.file_progress['value'] = 100
                    self.file_progress_label.config(text="完成")
        
        except Exception as e:
            self.queue_log(f"💥 多進程處理時發生錯誤: {str(e)}")
            self.queue_log("🔄 回退到單線程處理...")
            self.process_files_optimized()
            return
        
        total_time = time.time() - total_start_time
        speedup = (total_time * cpu_count) / total_time if total_time > 0 else 1
        
        self.queue_log(f"\n🚀 === 多進程處理完成 ===")
        self.queue_log(f"🔥 使用進程數: {cpu_count}")
        self.queue_log(f"✅ 成功: {success_count} 個檔案")
        self.queue_log(f"❌ 失敗: {error_count} 個檔案")
        self.queue_log(f"⚡ 總處理時間: {total_time:.2f} 秒")
        self.queue_log(f"📊 平均每檔案: {total_time/len(excel_files):.2f} 秒")
        self.queue_log(f"🚀 理論加速比: {cpu_count:.1f}x")
        
        self.finish_processing()
    
    def finish_processing(self):
        """完成處理"""
        self.start_button.config(state="normal", text="🚀 開始執行")
        # 完成所有進度條
        self.overall_progress['value'] = self.overall_progress['maximum'] if self.overall_progress['maximum'] else 0
        self.file_progress['value'] = 100
        self.file_progress_label.config(text="完成")

def main():
    # 設置高DPI支援
    try:
        from ctypes import windll
        windll.shcore.SetProcessDpiAwareness(1)
    except:
        pass
    
    root = tk.Tk()
    app = ExcelCheckGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()